import React from 'react';
import { useComponentsContext } from '@blocknote/react';
import { Wand2 } from 'lucide-react';

/**
 * Custom Restyle Button Component for BlockNote Floating Toolbar
 * Uses BlockNote's proper API for consistent tooltip positioning
 */
export const RestyleButton: React.FC = () => {
  const Components = useComponentsContext()!;

  const handleRestyle = () => {
    console.log('Restyle clicked');
  };

  return (
    <Components.FormattingToolbar.Button
      mainTooltip="Restyle"
      onClick={handleRestyle}
    >
      <Wand2 size={16} />
    </Components.FormattingToolbar.Button>
  );
};

// Simple FindPanel stub to fix import error
interface FindPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: any;
}

export const FindPanel: React.FC<FindPanelProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed top-4 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm font-medium">Find</span>
        <button onClick={onClose} className="text-sm">×</button>
      </div>
      <input
        type="text"
        placeholder="Search..."
        className="w-full px-2 py-1 border rounded text-sm"
      />
      <div className="mt-2 text-xs text-muted-foreground">
        Find functionality coming soon...
      </div>
    </div>
  );
};
