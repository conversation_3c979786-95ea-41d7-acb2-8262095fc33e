import React from 'react';
import { useComponentsContext } from '@blocknote/react';
import { Wand2 } from 'lucide-react';

/**
 * Custom Restyle Button Component for BlockNote Floating Toolbar
 * Uses BlockNote's proper API for consistent tooltip positioning
 */
export const RestyleButton: React.FC = () => {
  const Components = useComponentsContext()!;

  const handleRestyle = () => {
    console.log('Restyle clicked');
  };

  return (
    <Components.FormattingToolbar.Button
      mainTooltip="Restyle"
      onClick={handleRestyle}
    >
      <Wand2 size={16} />
    </Components.FormattingToolbar.Button>
  );
};

// Full FindPanel implementation
import { useState, useCallback, useRef, useEffect } from 'react';
import { X, ChevronUp, ChevronDown } from 'lucide-react';
import { BlockNoteEditor } from '@blocknote/core';

interface FindPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: BlockNoteEditor | null;
}

interface FindMatch {
  blockId: string;
  text: string;
  startIndex: number;
  endIndex: number;
}

interface FindResult {
  matches: FindMatch[];
  currentMatchIndex: number;
  totalMatches: number;
}

// Find text in document blocks
const findInDocument = (blocks: any[], searchTerm: string, caseSensitive: boolean = false): FindMatch[] => {
  const matches: FindMatch[] = [];
  const searchText = caseSensitive ? searchTerm : searchTerm.toLowerCase();

  const searchInBlock = (block: any) => {
    if (block.content && Array.isArray(block.content)) {
      const blockText = block.content
        .filter((item: any) => item.type === 'text')
        .map((item: any) => item.text)
        .join('');

      const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();
      let startIndex = 0;

      while (true) {
        const index = textToSearch.indexOf(searchText, startIndex);
        if (index === -1) break;

        matches.push({
          blockId: block.id,
          text: blockText.substring(index, index + searchTerm.length),
          startIndex: index,
          endIndex: index + searchTerm.length
        });

        startIndex = index + 1;
      }
    }

    if (block.children && Array.isArray(block.children)) {
      block.children.forEach(searchInBlock);
    }
  };

  blocks.forEach(searchInBlock);
  return matches;
};

export const FindPanel: React.FC<FindPanelProps> = ({ isOpen, onClose, editor }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [findResult, setFindResult] = useState<FindResult>({
    matches: [],
    currentMatchIndex: -1,
    totalMatches: 0
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const highlightedBlocksRef = useRef<Set<string>>(new Set());

  // Clear highlights
  const clearHighlights = useCallback(() => {
    const highlightedElements = document.querySelectorAll('[data-find-highlight]');
    highlightedElements.forEach(element => {
      element.removeAttribute('data-find-highlight');
    });
    highlightedBlocksRef.current.clear();
  }, []);

  // Apply highlights to matching blocks
  const applyHighlights = useCallback((matches: FindMatch[], currentIndex: number = -1) => {
    if (!editor || matches.length === 0) return;

    clearHighlights();
    const blockIds = new Set(matches.map(match => match.blockId));
    const currentBlockId = currentIndex >= 0 ? matches[currentIndex]?.blockId : null;

    blockIds.forEach(blockId => {
      setTimeout(() => {
        const blockElement = document.querySelector(`[data-id="${blockId}"]`);
        if (blockElement) {
          const isCurrent = blockId === currentBlockId;
          const highlightAttr = isCurrent ? 'current' : 'match';
          blockElement.setAttribute('data-find-highlight', highlightAttr);
          highlightedBlocksRef.current.add(blockId);
        }
      }, 10);
    });
  }, [editor, clearHighlights]);

  // Navigate to a specific match
  const navigateToMatch = useCallback((match: FindMatch) => {
    if (!editor) return;

    try {
      const block = editor.document.find(b => b.id === match.blockId);
      if (block) {
        editor.setTextCursorPosition(block, match.startIndex);

        // Scroll to the block
        setTimeout(() => {
          const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
          if (blockElement) {
            blockElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 50);
      }
    } catch (error) {
      console.error('Error navigating to match:', error);
    }
  }, [editor]);

  // Handle search
  const performSearch = useCallback((value: string) => {
    if (!editor || !value.trim()) {
      clearHighlights();
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      return;
    }

    try {
      const matches = findInDocument(editor.document, value, caseSensitive);
      const result: FindResult = {
        matches,
        currentMatchIndex: matches.length > 0 ? 0 : -1,
        totalMatches: matches.length
      };

      setFindResult(result);
      applyHighlights(matches, result.currentMatchIndex);

      if (matches.length > 0) {
        navigateToMatch(matches[0]);
      }
    } catch (error) {
      console.error('Error in search:', error);
    }
  }, [editor, caseSensitive, applyHighlights, clearHighlights, navigateToMatch]);

  // Handle search input changes
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);

    const timeoutId = setTimeout(() => {
      performSearch(value);
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [performSearch]);

  // Navigation functions
  const goToNextMatch = useCallback(() => {
    if (findResult.totalMatches === 0) return;

    const nextIndex = (findResult.currentMatchIndex + 1) % findResult.totalMatches;
    const newResult = { ...findResult, currentMatchIndex: nextIndex };
    setFindResult(newResult);
    applyHighlights(findResult.matches, nextIndex);
    navigateToMatch(findResult.matches[nextIndex]);
  }, [findResult, applyHighlights, navigateToMatch]);

  const goToPreviousMatch = useCallback(() => {
    if (findResult.totalMatches === 0) return;

    const prevIndex = findResult.currentMatchIndex === 0
      ? findResult.totalMatches - 1
      : findResult.currentMatchIndex - 1;
    const newResult = { ...findResult, currentMatchIndex: prevIndex };
    setFindResult(newResult);
    applyHighlights(findResult.matches, prevIndex);
    navigateToMatch(findResult.matches[prevIndex]);
  }, [findResult, applyHighlights, navigateToMatch]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        goToPreviousMatch();
      } else {
        goToNextMatch();
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  }, [goToNextMatch, goToPreviousMatch, onClose]);

  // Effects
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) {
      clearHighlights();
      setSearchTerm('');
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
    }
  }, [isOpen, clearHighlights]);

  // Display text for match count
  const displayText = findResult.totalMatches > 0
    ? `${findResult.currentMatchIndex + 1}/${findResult.totalMatches}`
    : searchTerm.trim() ? '0/0' : '';

  if (!isOpen) return null;

  return (
    <div className="fixed top-4 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg min-w-80 max-w-96">
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Find</span>
          <label className="flex items-center gap-1 text-xs">
            <input
              type="checkbox"
              checked={caseSensitive}
              onChange={(e) => {
                setCaseSensitive(e.target.checked);
                if (searchTerm.trim()) {
                  performSearch(searchTerm);
                }
              }}
              className="w-3 h-3"
            />
            Aa
          </label>
        </div>
        <button
          onClick={onClose}
          className="h-6 w-6 p-0 hover:bg-accent rounded flex items-center justify-center"
        >
          <X className="h-3 w-3" />
        </button>
      </div>

      <div className="p-3 space-y-3">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Find..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full h-8 text-sm pr-20 px-2 border border-border rounded"
            />
            <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <span className="text-xs text-muted-foreground min-w-12 text-center">
                {displayText}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={goToPreviousMatch}
              disabled={findResult.totalMatches === 0}
              className="h-8 w-8 p-0 hover:bg-accent rounded disabled:opacity-50 flex items-center justify-center"
              title="Previous match (Shift+Enter)"
            >
              <ChevronUp className="h-3 w-3" />
            </button>
            <button
              onClick={goToNextMatch}
              disabled={findResult.totalMatches === 0}
              className="h-8 w-8 p-0 hover:bg-accent rounded disabled:opacity-50 flex items-center justify-center"
              title="Next match (Enter)"
            >
              <ChevronDown className="h-3 w-3" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
