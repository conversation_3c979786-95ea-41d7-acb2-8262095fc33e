import React, { useState, useCallback, useRef, useEffect } from 'react';
import { TooltipProvider } from '@/ui/tooltip';
import { X, ChevronUp, ChevronDown } from 'lucide-react';
import { BlockNoteEditor } from '@blocknote/core';
import {
  findInDocument,
  navigateToMatch,
  createFindResult,
  getNextMatchIndex,
  getPreviousMatchIndex,
  type FindMatch,
  type FindResult
} from '../utils/findUtils';

interface FindPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: BlockNoteEditor | null;
}

export const FindPanel: React.FC<FindPanelProps> = ({
  isOpen,
  onClose,
  editor
}) => {
  // State management - always declare all state hooks first
  const [searchTerm, setSearchTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [findResult, setFindResult] = useState<FindResult>({
    matches: [],
    currentMatchIndex: -1,
    totalMatches: 0
  });

  // Refs - always declare all refs after state
  const searchInputRef = useRef<HTMLInputElement>(null);
  const highlightedBlocksRef = useRef<Set<string>>(new Set());
  const currentSearchTermRef = useRef<string>('');

  // All useCallback hooks - declare all callbacks before useEffect
  const clearHighlights = useCallback(() => {
    // Clear all highlighted blocks using direct DOM manipulation
    const allBlocks = document.querySelectorAll('.bn-block-outer');
    allBlocks.forEach(block => {
      block.classList.remove('find-block-highlight', 'find-block-highlight-current');
    });

    highlightedBlocksRef.current.clear();
    currentSearchTermRef.current = '';
  }, []);

  const applyHighlights = useCallback((matches: FindMatch[], currentIndex: number = -1) => {
    if (!editor || matches.length === 0) return;

    // Clear existing highlights first
    clearHighlights();

    // Get unique block IDs from matches
    const blockIds = new Set(matches.map(match => match.blockId));
    const currentBlockId = currentIndex >= 0 ? matches[currentIndex]?.blockId : null;

    // Apply highlighting to each unique block using direct DOM manipulation
    blockIds.forEach(blockId => {
      try {
        // Find the block element by data-id attribute
        const blockElement = document.querySelector(`[data-id="${blockId}"].bn-block-outer`);
        if (blockElement) {
          const isCurrent = blockId === currentBlockId;
          const highlightClass = isCurrent ? 'find-block-highlight-current' : 'find-block-highlight';

          // Remove any existing highlight classes first
          blockElement.classList.remove('find-block-highlight', 'find-block-highlight-current');
          // Add the appropriate highlight class
          blockElement.classList.add(highlightClass);

          highlightedBlocksRef.current.add(blockId);
        }
      } catch (error) {
        console.error('Error applying highlight to block:', blockId, error);
      }
    });

    currentSearchTermRef.current = searchTerm;
  }, [editor, searchTerm, clearHighlights]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);

    // Clear highlights if search is empty
    if (!value.trim()) {
      clearHighlights();
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      return;
    }

    // Debounce search to avoid excessive re-renders
    const timeoutId = setTimeout(() => {
      if (!editor) return;

      try {
        const matches = findInDocument(editor.document, value, caseSensitive);
        const result = createFindResult(matches, 0);
        setFindResult(result);
        applyHighlights(matches, result.currentMatchIndex);
      } catch (error) {
        console.error('Error in debounced search:', error);
      }
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [editor, caseSensitive, applyHighlights, clearHighlights]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        // Previous match (Shift+Enter)
        if (findResult.totalMatches > 0) {
          const prevIndex = getPreviousMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
          const newResult = { ...findResult, currentMatchIndex: prevIndex };
          setFindResult(newResult);
          applyHighlights(findResult.matches, prevIndex);
          if (findResult.matches[prevIndex]) {
            navigateToMatch(editor!, findResult.matches[prevIndex]);
          }
        }
      } else {
        // Next match (Enter)
        if (findResult.totalMatches > 0) {
          const nextIndex = getNextMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
          const newResult = { ...findResult, currentMatchIndex: nextIndex };
          setFindResult(newResult);
          applyHighlights(findResult.matches, nextIndex);
          if (findResult.matches[nextIndex]) {
            navigateToMatch(editor!, findResult.matches[nextIndex]);
          }
        }
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  }, [findResult, applyHighlights, editor, onClose]);

  const goToNextMatch = useCallback(() => {
    if (findResult.totalMatches === 0) return;

    const nextIndex = getNextMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    const newResult = { ...findResult, currentMatchIndex: nextIndex };
    setFindResult(newResult);
    applyHighlights(findResult.matches, nextIndex);

    if (findResult.matches[nextIndex] && editor) {
      navigateToMatch(editor, findResult.matches[nextIndex]);
    }
  }, [findResult, applyHighlights, editor]);

  const goToPreviousMatch = useCallback(() => {
    if (findResult.totalMatches === 0) return;

    const prevIndex = getPreviousMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    const newResult = { ...findResult, currentMatchIndex: prevIndex };
    setFindResult(newResult);
    applyHighlights(findResult.matches, prevIndex);

    if (findResult.matches[prevIndex] && editor) {
      navigateToMatch(editor, findResult.matches[prevIndex]);
    }
  }, [findResult, applyHighlights, editor]);

  const testHighlight = useCallback(() => {
    const firstBlock = document.querySelector('.bn-block-outer');
    if (firstBlock) {
      firstBlock.classList.add('find-block-highlight-current');
      console.log('Test highlight applied to:', firstBlock);
    } else {
      console.log('No .bn-block-outer elements found');
    }
  }, []);

  // All useEffect hooks - declare all effects after callbacks
  // Focus search input when panel opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Clear highlights when panel closes or search term changes
  useEffect(() => {
    if (!isOpen || !searchTerm.trim()) {
      clearHighlights();
    }
  }, [isOpen, searchTerm, clearHighlights]);

  // Set up MutationObserver to re-apply highlights when DOM changes
  useEffect(() => {
    if (!editor) return;

    const observer = new MutationObserver((mutations) => {
      // Check if any of our highlighted blocks were re-rendered
      let needsReapply = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // Check if this is a block element that should be highlighted
              if (element.classList.contains('bn-block-outer')) {
                const blockId = element.getAttribute('data-id');
                if (blockId && highlightedBlocksRef.current.has(blockId)) {
                  needsReapply = true;
                }
              }
              // Also check child elements
              const blockElements = element.querySelectorAll('.bn-block-outer[data-id]');
              blockElements.forEach((blockEl) => {
                const blockId = blockEl.getAttribute('data-id');
                if (blockId && highlightedBlocksRef.current.has(blockId)) {
                  needsReapply = true;
                }
              });
            }
          });
        }
      });

      // Re-apply highlights if needed
      if (needsReapply && findResult.matches.length > 0) {
        // Small delay to ensure DOM is fully updated
        setTimeout(() => {
          applyHighlights(findResult.matches, findResult.currentMatchIndex);
        }, 10);
      }
    });

    // Observe the editor container for changes
    const editorContainer = document.querySelector('.bn-editor');
    if (editorContainer) {
      observer.observe(editorContainer, {
        childList: true,
        subtree: true
      });
    }

    return () => {
      observer.disconnect();
    };
  }, [editor, findResult, applyHighlights]);

  // Listen for editor changes to clean up invalid highlights
  useEffect(() => {
    if (!editor || !searchTerm.trim()) return;

    const handleEditorChange = () => {
      // Small delay to ensure DOM is updated after the change
      setTimeout(() => {
        if (searchTerm.trim() && highlightedBlocksRef.current.size > 0) {
          // Re-run search to see if matches still exist
          try {
            const currentMatches = findInDocument(editor.document, searchTerm, caseSensitive);
            const currentBlockIds = new Set(currentMatches.map(match => match.blockId));

            // Remove highlights from blocks that no longer match
            highlightedBlocksRef.current.forEach(blockId => {
              if (!currentBlockIds.has(blockId)) {
                const blockElement = document.querySelector(`[data-id="${blockId}"].bn-block-outer`);
                if (blockElement) {
                  blockElement.classList.remove('find-block-highlight', 'find-block-highlight-current');
                  console.log(`Removed highlight from block ${blockId.substring(0, 8)}... (no longer matches)`);
                }
                highlightedBlocksRef.current.delete(blockId);
              }
            });

            // Update the find result if matches changed
            if (currentMatches.length !== findResult.totalMatches) {
              const newResult = createFindResult(currentMatches, 0);
              setFindResult(newResult);
              applyHighlights(currentMatches, newResult.currentMatchIndex);
            }
          } catch (error) {
            console.error('Error updating highlights after editor change:', error);
          }
        }
      }, 100);
    };

    // Add the change listener
    editor.onChange(handleEditorChange);

    // Cleanup function to remove the listener
    return () => {
      // Note: BlockNote doesn't provide a direct way to remove onChange listeners
      // The listener will be cleaned up when the editor is recreated
    };
  }, [editor, searchTerm, caseSensitive, findResult.totalMatches, applyHighlights]);

  // Display text for match count
  const displayText = findResult.totalMatches > 0
    ? `${findResult.currentMatchIndex + 1}/${findResult.totalMatches}`
    : searchTerm.trim() ? '0/0' : '';

  if (!isOpen) return null;

  return (
    <TooltipProvider delayDuration={100}>
      <div className="fixed top-4 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg min-w-80 max-w-96">
        <div className="flex items-center justify-between p-3 border-b border-border">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Find & Replace</span>
          </div>
          <button
            onClick={onClose}
            className="h-6 w-6 p-0 hover:bg-accent rounded"
          >
            <X className="h-3 w-3" />
          </button>
        </div>

        <div className="p-3 space-y-3">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Find... (Enter to search)"
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full h-8 text-sm pr-24 px-2 border border-border rounded"
              />
              <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center gap-1">
                <span className="text-xs text-muted-foreground min-w-16 text-center">
                  {displayText}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={goToPreviousMatch}
                disabled={findResult.totalMatches === 0}
                className="h-8 w-8 p-0 hover:bg-accent rounded disabled:opacity-50"
              >
                <ChevronUp className="h-3 w-3" />
              </button>
              <button
                onClick={goToNextMatch}
                disabled={findResult.totalMatches === 0}
                className="h-8 w-8 p-0 hover:bg-accent rounded disabled:opacity-50"
              >
                <ChevronDown className="h-3 w-3" />
              </button>
            </div>
          </div>

          {/* Test button for debugging */}
          <div className="flex items-center gap-2 pt-2 border-t border-border">
            <button
              onClick={testHighlight}
              className="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600"
            >
              Test Highlight
            </button>
            <button
              onClick={clearHighlights}
              className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
            >
              Clear All
            </button>
            <span className="text-xs text-muted-foreground">
              Debug: {highlightedBlocksRef.current.size} highlighted
            </span>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};
