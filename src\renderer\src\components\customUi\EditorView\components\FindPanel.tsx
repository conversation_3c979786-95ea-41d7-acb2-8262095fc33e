import React from 'react'; // Removed unused hooks
import { TooltipProvider } from '@/ui/tooltip';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

// export const FindPanel: React.FC<FindPanelProps> = ({
//   isOpen,
//   onClose,
//   editor
// }) => {
//   ...existing code...
// };

// Minimal test: just a button that applies the border to the first block once
export const FindPanel = () => {
  // Add this function to your FindPanel component
  const testHighlight = () => {
    const firstBlock = document.querySelector('.bn-block-outer');
    if (firstBlock) {
      firstBlock.classList.add('find-block-highlight-current');
      console.log('Test highlight applied to:', firstBlock);
    }
  };

  return (
    <TooltipProvider delayDuration={100}>
      <div className="fixed top-4 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg min-w-80 max-w-96">
        <div className="flex items-center justify-between p-3 border-b border-border">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Find & Replace</span>
          </div>
          <button variant="ghost" size="sm" onClick={() => {}} className="h-6 w-6 p-0">
            <X className="h-3 w-3" />
          </button>
        </div>

        <div className="p-3 space-y-3">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Find... (Enter to search)"
                className="h-8 text-sm pr-24"
              />
              <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center gap-1">
                <span className="text-xs text-muted-foreground min-w-16 text-center">
                  {/* {displayText} */}
                </span>
              </div>
            </div>
            <button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="h-8 px-2 text-xs"
              disabled={true}
            >
              Search
            </button>
            <div className="flex items-center gap-1">
              <button
                variant="ghost"
                size="sm"
                onClick={() => {}}
                disabled={true}
                className="h-8 w-8 p-0"
              >
                {/* <ChevronUp className="h-3 w-3" /> */}
              </button>
              <button
                variant="ghost"
                size="sm"
                onClick={() => {}}
                disabled={true}
                className="h-8 w-8 p-0"
              >
                {/* <ChevronDown className="h-3 w-3" /> */}
              </button>
            </div>
          </div>

          {/* {showReplace && (
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  ref={replaceInputRef}
                  type="text"
                  placeholder="Replace..."
                  value={replaceTerm}
                  onChange={(e) => setReplaceTerm(e.target.value)}
                  onKeyDown={handleReplaceKeyDown}
                  className="h-8 text-sm"
                />
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={replaceCurrentMatch}
                  disabled={findResult.totalMatches === 0 || !replaceTerm || findResult.currentMatchIndex < 0}
                  className="h-8 px-2 text-xs"
                >
                  Replace
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={replaceAllMatches}
                  disabled={findResult.totalMatches === 0 || !replaceTerm}
                  className="h-8 px-2 text-xs"
                >
                  All
                </Button>
              </div>
            </div>
          )} */}

          {/* <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCaseSensitive(!caseSensitive)}
                className={cn("h-8 w-8 p-0", caseSensitive && "bg-accent text-accent-foreground")}
              >
                <CaseSensitive className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReplace(!showReplace)}
                className={cn("h-8 w-8 p-0", showReplace && "bg-accent text-accent-foreground")}
              >
                <Replace className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowResults(!showResults)}
                disabled={findResult.totalMatches === 0}
                className={cn("h-8 w-8 p-0", showResults && "bg-accent text-accent-foreground")}
              >
                <List className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex gap-1">
              <button
                onClick={testHighlight}
                className="px-2 py-1 bg-red-500 text-white"
              >
                Test Highlight
              </button>
            </div>
          </div> */}

          {/* {showResults && findResult.matches.length > 0 && (
            <div className="border-t border-border pt-3">
              <div className="text-xs text-muted-foreground mb-2">
                {findResult.totalMatches} result{findResult.totalMatches !== 1 ? 's' : ''}
              </div>
              <div className="max-h-60 overflow-y-auto space-y-1 pr-1 custom-scrollbar">
                {findResult.matches.map((match, index) => (
                  <button
                    key={`${match.blockId}-${match.textIndex}-${index}`} // Added index for more unique key
                    onClick={() => goToMatch(index)}
                    className={cn(
                      "w-full text-left p-2 rounded text-xs hover:bg-accent hover:text-accent-foreground transition-colors",
                      index === findResult.currentMatchIndex && "bg-accent text-accent-foreground"
                    )}
                  >
                    <div className="truncate">
                      {match.blockText.substring(0, match.textIndex)}
                      <span className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
                        {match.text}
                      </span>
                      {match.blockText.substring(match.textIndex + match.length)}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )} */}
        </div>

        <div className="p-4">
          <button
            onClick={testHighlight}
            className="px-2 py-1 bg-red-500 text-white"
          >
            Test Highlight
          </button>
        </div>
      </div>
    </TooltipProvider>
  );
};
