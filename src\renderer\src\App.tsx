import React, { FC, useEffect, useCallback } from 'react';
import HoverSidebarLeft from '@/components/customUi/sidebar-left';
import { NoDocumentPlaceholder } from '@/components/customUi/NoDocumentPlaceholder';
import fileSystemStorage from '@/lib/file-system-storage';
import { Button } from '@/ui/button';
import { ArrowRight } from 'lucide-react';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { SidebarProvider, useSidebarContext } from '@/contexts/SidebarContext';
import CustomTitleBar from '@/components/customUi/CustomTitleBar';
import TabsBar from '@/components/customUi/TabsBar';
import { NotificationProvider, useNotification } from '@/contexts/NotificationContext';
import { SettingsSidebar, SettingsSidebarProvider } from '@/components/customUi/sidebar-right';
import { useDocumentStore } from '@/stores/blockDocumentStore';
import EditorView from '@/components/customUi/EditorView/EditorView';
import EditorKeyboardShortcuts from '@/components/customUi/EditorKeyboardShortcuts';
import AppLogoSvg from '@/components/customUi/AppLogoSvg';
import { useTheme } from '@/hooks/useTheme';
import { ManageSavedParagraphsModalContainer } from '@/components/customUi/manage-saved-paragraphs-modal';
import { useSavedParagraphsStore } from '@/stores/savedParagraphsStore';
import { FindDialogProvider, useFindDialog } from '@/contexts/FindDialogContext';
import { FindPanel } from '@/components/customUi/EditorView/components/RestyleButton';
import { useDocumentStore as useDocumentStoreForEditor } from '@/stores/blockDocumentStore';

const AppContent: FC = () => {
  // Get state and methods from the Zustand store
  const {
    currentStoragePath,
    activeFileId,
    refreshFiles,
    selectStorageDirectory,
    setCurrentStoragePath,
    unloadWorkspace
  } = useDocumentStore();

  const { showNotification } = useNotification();
  const { isLeftSidebarExpanded } = useSidebarContext();
  const { isDarkMode } = useTheme();

  // Saved paragraphs modal state
  const { isModalOpen, closeModal } = useSavedParagraphsStore();

  // Find dialog state
  const { isFindDialogOpen, setIsFindDialogOpen } = useFindDialog();

  // Get current editor instance for FindPanel
  const currentEditor = useDocumentStoreForEditor(state => state.currentEditor);

  // Handle modal close with UI extension state
  const handleModalClose = useCallback(() => {
    closeModal();
    // Note: UI extension state is managed by the sidebar component that opens the modal
  }, [closeModal]);

  // Initialize the application on mount
  useEffect(() => {
    const initializeApp = async () => {
      // console.log('[App] initializeApp started');

      // Wait for the API to be available if needed
      const maxRetries = 10;
      let retries = 0;
      while (!window.fileStorage && retries < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 100));
        retries++;
      }

      if (!window.fileStorage) {
        console.error('[App] initializeApp: window.fileStorage is not available after retries');
        showNotification('Error: File storage API not available.', 'error');
        return;
      }

      // Check if there's a storage path
      const path = await window.fileStorage.getStoragePath(); // Use await here
      // console.log('[App] initializeApp: Storage path from window.fileStorage:', path);
      setCurrentStoragePath(path);

      if (path) {
        // console.log('[App] initializeApp: Storage path exists. Loading project settings and refreshing files.');
        try {
          // It's crucial to load project settings first, or ensure it's loaded before components that need it render.
          await useDocumentStore.getState().loadProjectSettings();
          console.log('[App] initializeApp: Project settings loaded successfully.'); // Essential log
        } catch (error) {
          console.error('[App] initializeApp: Error loading project settings:', error);
          showNotification('Error loading project settings.', 'error');
        }
        try {
          await refreshFiles();
          // console.log('[App] initializeApp: Files refreshed.');
        } catch (error) {
          console.error('[App] initializeApp: Error loading files:', error);
          showNotification('Error loading files from storage.', 'error');
        }
      } else {
        // console.log('[App] initializeApp: No storage path found.');
      }
      // console.log('[App] initializeApp finished');
    };

    initializeApp();
    // Ensure loadProjectSettings is part of dependencies if it were passed as a prop
    // Since we are calling it via getState(), it's not a direct dependency here,
    // but be mindful of how Zustand state changes might interact if not careful.
  }, [refreshFiles, setCurrentStoragePath, showNotification]);

  // Handle storage directory selection
  const handleSelectStorageFolder = async () => {
    try {
      await selectStorageDirectory();
      showNotification('Storage directory selected successfully.', 'success');
    } catch (error) {
      console.error('Error selecting storage directory:', error);
      showNotification('Error selecting storage directory.', 'error');
    }
  };

  // Handle workspace unloading
  const handleUnloadWorkspace = async () => {
    try {
      await unloadWorkspace();
      showNotification('Workspace unloaded successfully.', 'success');
    } catch (error) {
      console.error('Error unloading workspace:', error);
      showNotification('Error unloading workspace.', 'error');
    }
  };

  return (
    <SettingsSidebarProvider>
      <CustomTitleBar />
      <div
        className="app flex flex-col h-screen antialiased text-foreground bg-background"
        style={{ paddingTop: '30px' }} // Adjusted for CustomTitleBar height
      >
        <div className="border-b border-border">
          <TabsBar />
        </div>
        <div className="flex flex-1 overflow-hidden">
          <HoverSidebarLeft
            onSelectStorageFolder={handleSelectStorageFolder}
          />
          <main
            className="flex-1 overflow-auto px-6 pb-6 transition-all duration-300 ease-in-out"
            style={{
              marginLeft: '32px', /* Corresponds to collapsed left sidebar width */
              marginRight: '32px',
              // Apply pointer events control when sidebar is expanded
              ...(isLeftSidebarExpanded && {
                isolation: 'isolate',
                // Disable interactions on the left portion that's behind the sidebar
                background: `linear-gradient(to right,
                  transparent 0%,
                  transparent 318px,
                  var(--background) 318px,
                  var(--background) 100%
                )`,
                pointerEvents: 'auto'
              })
            }}
          >
            {/* Overlay to block interactions behind sidebar */}
            {isLeftSidebarExpanded && (
              <div
                className="fixed left-8 w-[318px] pointer-events-none"
                style={{
                  top: '30px', // Full height overlay when sidebar is expanded
                  height: 'calc(100vh - 30px)', // Full height overlay when sidebar is expanded
                  zIndex: 1
                }}
              />
            )}

            <div
              className="relative"
              style={{
                // Add containment to prevent BlockNote elements from escaping
                contain: 'layout style paint',
                // Ensure content area respects the interaction blocking
                ...(isLeftSidebarExpanded && {
                  marginLeft: '286px' // 318px sidebar - 32px existing margin
                })
              }}
            >
              {!currentStoragePath ? (
                <NoDocumentPlaceholder />
              ) : !activeFileId ? (
                <div className="flex flex-col items-center justify-center h-full text-center p-10 bg-background">
                  <div className="p-6 bg-card rounded-xl shadow-sm border border-border/50 max-w-lg w-full">
                    <div className="flex justify-center mb-6">
                      <AppLogoSvg
                        className="w-24 h-24"
                        fillColor={isDarkMode ? "#3E3629" : "#F5F1E8"}
                        strokeColor={isDarkMode ? "#D4B896" : "#8B4513"}
                        eyeColor={isDarkMode ? "#D4B896" : "#8B4513"}
                        strokeWidthValue={30}
                      />
                    </div>
                    <h2 className="text-2xl font-semibold mb-4 text-foreground">Workspace Ready</h2>
                    <p className="text-md text-muted-foreground mb-3">
                      Your storage folder is set to: <strong className="font-medium text-muted-foreground">{currentStoragePath}</strong>.
                    </p>
                    <p className="text-md text-muted-foreground mb-3">
                      Select a document from the sidebar to begin editing, or create a new document or folder using the '+' icons in the sidebar.
                    </p>
                    <button
                      onClick={handleUnloadWorkspace}
                      className="text-sm text-muted-foreground hover:text-foreground underline transition-colors mt-2"
                    >
                      Unload workspace
                    </button>
                  </div>
                </div>
              ) : (
                <EditorView />
              )}
            </div>
          </main>
          <SettingsSidebar />
        </div>
      </div>
      <EditorKeyboardShortcuts />

      {/* Global Find Panel */}
      <FindPanel
        isOpen={isFindDialogOpen}
        onClose={() => setIsFindDialogOpen(false)}
        editor={currentEditor}
      />

      {/* Global Saved Paragraphs Modal */}
      <ManageSavedParagraphsModalContainer
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />
    </SettingsSidebarProvider>
  );
};

const App: FC = () => {
  return (
    <ThemeProvider>
      <NotificationProvider>
        <SidebarProvider>
          <FindDialogProvider>
            <AppContent />
          </FindDialogProvider>
        </SidebarProvider>
      </NotificationProvider>
    </ThemeProvider>
  );
};

export default App;
